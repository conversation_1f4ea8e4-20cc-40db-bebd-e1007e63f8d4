﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace A2A.Data.Interop.Noaa.Ioos_061
{
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.noaa.gov/ioos/0.6.1")]
    [System.Xml.Serialization.XmlRootAttribute("VerticalDatum", Namespace = "http://www.noaa.gov/ioos/0.6.1", IsNullable = false)]
    public class VerticalDatumScalarType
    {
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute("epoch")]
        public string Epoch { get; set; }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value { get; set; }
    }
}
