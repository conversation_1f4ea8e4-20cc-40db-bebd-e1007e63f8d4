﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace A2A.Data.Interop.Noaa.Ioos_061
{
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.noaa.gov/ioos/0.6.1")]
    [System.Xml.Serialization.XmlRootAttribute("VerticalPosition", Namespace = "http://www.noaa.gov/ioos/0.6.1", IsNullable = false)]
    public class VerticalPositionScalarType
    {
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute("uom")]
        public string Uom { get; set; }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public float Value { get; set; }
    }
}
