﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace A2A.Data.Interop.Noaa.Ioos_061
{
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.noaa.gov/ioos/0.6.1")]
    [System.Xml.Serialization.XmlRootAttribute("Text", Namespace = "http://www.noaa.gov/ioos/0.6.1", IsNullable = true)]
    public class NamedTextType
    {
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute("nilReason")]
        public string NilReason { get; set; }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public float Value { get; set; }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute("name")]
        public string Name { get; set; }
    }
}
