﻿using A2A.Data.Interop.OpenGIS.Gml_32;

namespace A2A.Data.Interop.Noaa.Ioos_061
{
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.noaa.gov/ioos/0.6.1")]
    [System.Xml.Serialization.XmlRootAttribute("Quantity", Namespace = "http://www.noaa.gov/ioos/0.6.1", IsNullable = true)]
    public class NamedQuantityType : MeasureType
    {
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute("nilReason")]
        public string NilReason { get; set; }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute("name")]
        public string Name { get; set; }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute("standardName")]
        public string StandardName { get; set; }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute("longName")]
        public string LongName { get; set; }
    }
}
