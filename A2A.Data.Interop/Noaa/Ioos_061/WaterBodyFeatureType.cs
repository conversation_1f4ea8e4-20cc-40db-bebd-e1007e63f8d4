﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using A2A.Data.Interop.OpenGIS.Gml_32;

namespace A2A.Data.Interop.Noaa.Ioos_061
{
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.noaa.gov/ioos/0.6.1")]
    [System.Xml.Serialization.XmlRootAttribute("WaterBodyFeature", Namespace = "http://www.noaa.gov/ioos/0.6.1", IsNullable = false)]
    public class WaterBodyFeatureType : AbstractFeatureType
    {
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("surface")]
        public SurfacePropertyType Surface { get; set; }
    }
}
