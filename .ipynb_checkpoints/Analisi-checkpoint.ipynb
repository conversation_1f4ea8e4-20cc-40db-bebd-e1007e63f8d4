{"cells": [{"cell_type": "code", "execution_count": 6, "id": "391aaab2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Indicizza tutti i file .cs e trova i namespace/classi definiti...\n", "Numero di file trovati per questa verticale: 5062\n", "Copiati 5062 file in /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/OUTPUT_VERTICALE_1\n"]}], "source": ["import os\n", "import re\n", "import shutil\n", "from collections import deque\n", "\n", "# ============================\n", "# CONFIGURAZIONE\n", "# ============================\n", "# Cartella radice della tua solution .NET (dove cercare ricorsivamente i file .cs)\n", "ROOT_DIR = os.getcwd()\n", "\n", "# Lista dei file \"entry point\" (es.: front-end) che definiscono la verticale;\n", "# puoi mettere i percorsi completi o parziali relativi a ROOT_DIR.\n", "ENTRY_POINTS = [\n", "    \"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/NBDO/UC/Pianificazione/Budget/CreateBudget.ascx\"\n", "    # Aggiungi qui altri file di front-end (controller, form, page, etc.) relativi a una data vertical\n", "]\n", "\n", "# Cartella di destinazione in cui copiare tutto il codice della verticale\n", "current_dir=os.getcwd()\n", "DEST_DIR = current_dir+\"/OUTPUT_VERTICALE_1\"\n", "\n", "# Espressione regolare per cercare i \"using\" delle classi (semplificato, da adattare)\n", "# Esempio: using MyCompany.Services;  (namespace)\n", "USING_REGEX = re.compile(r'^\\s*using\\s+([A-Za-z0-9_.]+)\\s*;')\n", "\n", "# <PERSON>sp<PERSON><PERSON> regolare per trovare dove viene referenziato un simbolo\n", "# Esempio: new MyService(...) o: MyCompany.Services.MyService\n", "# (<PERSON><PERSON><PERSON> basilar<PERSON>: potrebbe creare falsi positivi.)\n", "CLASS_REF_REGEX = re.compile(r'([A-Za-z0-9_.]+)\\b')\n", "\n", "# ============================\n", "# FUNZIONI DI SUPPORTO\n", "# ============================\n", "\n", "def trova_file_cs(root_dir):\n", "    \"\"\"Ritorna la lista di tutti i file .cs nella cartella root_dir (ricorsivamente).\"\"\"\n", "    lista = []\n", "    for base, dirs, files in os.walk(root_dir):\n", "        for f in files:\n", "            if f.lower().endswith(\".cs\"):\n", "                full_path = os.path.join(base, f)\n", "                lista.append(full_path)\n", "    return lista\n", "\n", "def leggi_file(path):\n", "    \"\"\"Legge l'intero contenuto di un file di testo (UTF-8).\"\"\"\n", "    with open(path, 'r', encoding='utf-8', errors='ignore') as f:\n", "        return f.read()\n", "\n", "def estrai_namespace_e_class(path):\n", "    \"\"\"\n", "    Estrae il namespace (approssimativo) e i nomi di classi pubbliche dal file di input\n", "    per poter poi capire 'dove' è definita una classe/namespace.\n", "    \"\"\"\n", "    text = leggi_file(path)\n", "\n", "    # Cerca \"namespace XYZ...\" in modo basilare\n", "    ns_match = re.search(r'namespace\\s+([A-Za-z0-9_.]+)', text)\n", "    if ns_match:\n", "        namespace = ns_match.group(1)\n", "    else:\n", "        namespace = None\n", "\n", "    # Cerca eventuali classi (public class NomeClasse)\n", "    classi = re.findall(r'public\\s+class\\s+([A-Za-z0-9_]+)', text)\n", "    # Oppure, se vuoi includere internal, etc.: re.findall(r'(public|internal)?\\s*class\\s+([A-Za-z0-9_]+)', text)\n", "\n", "    return namespace, classi\n", "\n", "def trova_dipendenze_in_file(path):\n", "    \"\"\"\n", "    Ritorna una lista di 'namespace' o 'simboli' usati in un file .cs, in base al \"using\" e a un parse naive del testo.\n", "    \"\"\"\n", "    text = leggi_file(path)\n", "\n", "    # 1. <PERSON><PERSON><PERSON> i using\n", "    usings = USING_REGEX.findall(text)\n", "\n", "    # 2. T<PERSON>a riferimenti a classi/namespace\n", "    #    (Molto approssimativo!)\n", "    #    <PERSON><PERSON><PERSON> filtrare su stringhe che contengono un punto (MyCompany.Something).\n", "    class_refs = CLASS_REF_REGEX.findall(text)\n", "\n", "    # Se vuoi, potresti unire e ritornare un set\n", "    return set(usings + class_refs)\n", "\n", "# ============================\n", "# LOGICA PRINCIPALE\n", "# ============================\n", "\n", "def main():\n", "    # 1. Mappa \"namespace o classe\" -> elenco di file che la definiscono\n", "    #    (<PERSON>sì possiamo trovare il file .cs responsabile di un certo simbolo.)\n", "    print(\"Indicizza tutti i file .cs e trova i namespace/classi definiti...\")\n", "    tutti_file = trova_file_cs(ROOT_DIR)\n", "\n", "    # Dato che una classe potrebbe chiamarsi X e un namespace potrebbe essere MyApp.X, ecc.\n", "    # faremo una mappa semplificata: { \"MyApp.Services\": [fileX, fileY], \"MioController\": [fileZ], ... }\n", "    definizioni_map = {}\n", "\n", "    for cs_file in tutti_file:\n", "        ns, classi = estrai_namespace_e_class(cs_file)\n", "        if ns:\n", "            definizioni_map.setdefault(ns, []).append(cs_file)\n", "        for c in classi:\n", "            definizioni_map.setdefault(c, []).append(cs_file)\n", "\n", "    # 2. Avvia la ricerca BFS/DFS partendo dai file \"entry point\"\n", "    to_visit = deque()\n", "    visited_files = set()  # File .cs già inclusi\n", "    needed_files = set()   # File effettivamente inclusi nella vertical slice\n", "\n", "    # Metti in coda i path \"entry point\"\n", "    for ep in ENTRY_POINTS:\n", "        entry_abs = os.path.join(ROOT_DIR, ep)\n", "        if os.path.isfile(entry_abs):\n", "            to_visit.append(entry_abs)\n", "        else:\n", "            print(f\"ATTENZIONE: file entry point non trovato: {entry_abs}\")\n", "\n", "    while to_visit:\n", "        current_file = to_visit.popleft()\n", "        if current_file in visited_files:\n", "            continue\n", "        visited_files.add(current_file)\n", "\n", "        # Aggiungi subito questo file alla \"vertical slice\"\n", "        needed_files.add(current_file)\n", "\n", "        # Trova i simboli referenziati\n", "        deps = trova_dipendenze_in_file(current_file)\n", "        # Per ognuno, cerca i file in cui è definito\n", "        for d in deps:\n", "            if d in definizioni_map:\n", "                for fdef in definizioni_map[d]:\n", "                    if fdef not in visited_files:\n", "                        to_visit.append(fdef)\n", "\n", "    # 3. Ora 'needed_files' contiene tutti i file .cs coinvolti direttamente/indirettamente\n", "    #    dalla verticale (molto approssimativo, ma è un inizio).\n", "    print(f\"Numero di file trovati per questa verticale: {len(needed_files)}\")\n", "\n", "    # 4. Copia i file in DEST_DIR, mantenendo la stessa struttura di cartelle\n", "    for nf in needed_files:\n", "        # Percorso relativo rispetto a ROOT_DIR\n", "        rel_path = os.path.relpath(nf, ROOT_DIR)\n", "        dest_path = os.path.join(DEST_DIR, rel_path)\n", "\n", "        os.makedirs(os.path.dirname(dest_path), exist_ok=True)\n", "        shutil.copy2(nf, dest_path)\n", "\n", "    print(f\"Copiati {len(needed_files)} file in {DEST_DIR}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": 11, "id": "757904bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON> coinvolti: 10\n", "  - ('CreateBudget', 'btnCrea_Click')\n", "  - ('DlgWinBox', 'ShowMessage')\n", "  - ('UP', 'ListaToString')\n", "  - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CreatePluriennaleImpianto')\n", "  - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CheckInsert')\n", "  - ('UP', 'ToString')\n", "  - ('DlgWinBox', 'ShowError')\n", "  - ('DlgWinBox', 'Inform')\n", "  - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CreateAnnualeImpianto')\n", "  - ('Create<PERSON><PERSON><PERSON>', 'CreateRiprevisioneImpianto')\n", "File coinvolti: 3\n", "  - /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/NBDOLib/DLGWINBOX.CS\n", "  - /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/NBDO/UC/Pianificazione/Budget/CreateBudget.ascx.cs\n", "  - /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/NBDOBusinessLogic/Integrazione/ClassiChiamata/ImportazioneNMO13.cs\n", "Copiati i file in: /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/verticale\n"]}], "source": ["import json\n", "import os\n", "from collections import deque, defaultdict\n", "\n", "# ======================================================\n", "# CONFIGURAZIONE\n", "# ======================================================\n", "# Path al file JSON da 32MB (contenente l’elenco di file, classi, metodi, dipendenze)\n", "JSON_PATH = \"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/report.json\"\n", "\n", "# L'entry point da cui partire (ad esempio un metodo in un file front-end)\n", "# Se sai che \"CreateBudget.ascx.cs\" ha il metodo \"btnCrea_Click\" come \"entry point\"\n", "# potresti impostare:\n", "ENTRY_CLASS = \"CreateBudget\"       # ClassName\n", "ENTRY_METHOD = \"btnCrea_Click\"     # MethodName\n", "\n", "# Cartella dove (opzionalmente) vuoi copiare i file di questa vertical\n", "OUTPUT_FOLDER = \"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/verticale\"\n", "\n", "\n", "# ======================================================\n", "# SCRIPT\n", "# ======================================================\n", "\n", "def carica_dati(json_path):\n", "    \"\"\"Carica l'intero JSON (32 MB) in memoria come Python list/dict.\"\"\"\n", "    with open(json_path, \"r\", encoding=\"utf-8\") as f:\n", "        data = json.load(f)\n", "    return data\n", "\n", "def indicizza_dati(data):\n", "    \"\"\"\n", "    Crea un dizionario:\n", "       { (<PERSON>N<PERSON>, MethodName): {\n", "             'FilePath': ...,\n", "             'Dependencies': [...],\n", "             ...\n", "         }, ... }\n", "\n", "    <PERSON><PERSON><PERSON> possiamo, dato (classe, metodo), trovare rapidamente i suoi attributi.\n", "    \"\"\"\n", "    index = {}\n", "    for entry in data:\n", "        file_path = entry[\"FilePath\"]\n", "        class_name = entry[\"ClassName\"]\n", "        methods = entry.get(\"Methods\", [])\n", "        for m in methods:\n", "            key = (class_name, m[\"MethodName\"])\n", "            index[key] = {\n", "                \"FilePath\": file_path,\n", "                \"ClassName\": class_name,\n", "                \"MethodName\": m[\"MethodName\"],\n", "                \"LineCount\": m.get(\"LineCount\", 0),\n", "                \"CyclomaticComplexity\": m.get(\"CyclomaticComplexity\", 0),\n", "                \"Dependencies\": m.get(\"Dependencies\", [])\n", "            }\n", "    return index\n", "\n", "def estrai_verticale(index, start_class, start_method):\n", "    \"\"\"\n", "    Esegue BFS partendo da (start_class, start_method).\n", "    Per ogni metodo trovato, cerca le sue dipendenze e, se corrispondono a metodi custom\n", "    presenti nell'indice, li aggiunge alla coda.\n", "    Ritorna l'insieme dei (class, method) e i path .cs coinvolti.\n", "    \"\"\"\n", "    visited = set()\n", "    queue = deque()\n", "\n", "    # Chiave iniziale\n", "    start_key = (start_class, start_method)\n", "    if start_key not in index:\n", "        print(f\"ERRORE: {start_key} non trovato nell'indice.\")\n", "        return set(), set()\n", "\n", "    queue.append(start_key)\n", "    visited_methods = set()    # conterrà (class,method)\n", "    visited_files = set()      # conterrà i path .cs\n", "\n", "    while queue:\n", "        current = queue.popleft()\n", "        if current in visited_methods:\n", "            continue\n", "        visited_methods.add(current)\n", "\n", "        # recuperiamo info\n", "        info = index[current]\n", "        file_path = info[\"FilePath\"]\n", "        visited_files.add(file_path)\n", "\n", "        # cer<PERSON><PERSON> le dipendenze\n", "        for dep in info[\"Dependencies\"]:\n", "            # Esempio di dep: \"CreateAnnualeImpianto\"\n", "            # oppure \"MiaClasse.MioMetodo\"\n", "            # Dobbiamo vedere se corrisponde a (class, method)\n", "\n", "            # 1) Se c'è un punto \".\", proviamo a split su \".\"\n", "            #    e ipotizziamo (depClass, depMethod)\n", "            # 2) <PERSON><PERSON><PERSON><PERSON>, potre<PERSON> essere un method nella stessa classe\n", "            if \".\" in dep:\n", "                # Potrebbe essere \"Classe.Metodo\"\n", "                parts = dep.split(\".\")\n", "                # se c'è piu di 2 parti, potresti dover gestire un namespace, e non\n", "                # un ClassName e MethodName\n", "                # per semplicità assumiamo ClassName=parts[0], MethodName=parts[1]\n", "                dep_class, dep_method = parts[0], parts[-1]\n", "                dep_key = (dep_class, dep_method)\n", "                if dep_key in index:\n", "                    queue.append(dep_key)\n", "            else:\n", "                # potremmo assumere che il dep sia un metodo della stessa classe\n", "                # (oppure un metodo di libreria .NET)\n", "                dep_key = (info[\"ClassName\"], dep)\n", "                if dep_key in index:\n", "                    queue.append(dep_key)\n", "\n", "    return visited_methods, visited_files\n", "\n", "\n", "def copia_file_verticale(files_set, output_folder):\n", "    \"\"\"\n", "    Copia i file .cs in output_folder mantenendo la struttura di cartelle.\n", "    \"\"\"\n", "    import shutil\n", "\n", "    for fpath in files_set:\n", "        # costruiamo un path relativo\n", "        # se i .cs stanno sotto un root comune, puoi estrarre il relpath\n", "        rel_path = os.path.relpath(fpath, start=os.path.commonpath(files_set))\n", "        dest_path = os.path.join(output_folder, rel_path)\n", "        os.makedirs(os.path.dirname(dest_path), exist_ok=True)\n", "\n", "        # copiamo\n", "        if os.path.isfile(fpath):\n", "            shutil.copy2(fpath, dest_path)\n", "        else:\n", "            print(f\"ATTENZIONE: file non trovato {fpath}\")\n", "\n", "\n", "def main():\n", "    data = carica_dati(JSON_PATH)\n", "    index = indicizza_dati(data)\n", "\n", "    visited_methods, visited_files = estrai_verticale(index, ENTRY_CLASS, ENTRY_METHOD)\n", "\n", "    print(f\"Metodi coinvolti: {len(visited_methods)}\")\n", "    for cm in visited_methods:\n", "        print(\"  -\", cm)\n", "\n", "    print(f\"File coinvolti: {len(visited_files)}\")\n", "    for f in visited_files:\n", "        print(\"  -\", f)\n", "\n", "    # Se vuoi copiare i file in OUTPUT_FOLDER, decommenta:\n", "    copia_file_verticale(visited_files, OUTPUT_FOLDER)\n", "    print(\"Copiati i file in:\", OUTPUT_FOLDER)\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": 15, "id": "43d4c630", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON> di partenza (entry points): 19\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>_Load')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'FillUP')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Fill<PERSON>dro')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Fill<PERSON><PERSON><PERSON>')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CheckInsert')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'OnInit')\n", " - ('Create<PERSON>udget', 'InitializeComponent')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'DdlTipologiaEsercizio_SelectedIndexChanged')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'DdlCentraliNucleo_SelectedIndexChanged')\n", " - ('CreateBudget', 'btnCrea_Click')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CreateAnnualeImpianto')\n", " - ('CreateBudget', 'CreateAnnualeImpianto_ConBloccoNoPdmProBudget')\n", " - ('Create<PERSON><PERSON><PERSON>', 'CreateAnnualeImpianto_old')\n", " - ('Create<PERSON><PERSON><PERSON>', 'CreateRiprevisioneImpianto')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CreatePluriennaleImpianto')\n", " - ('CreateBudget', 'btnProcedi_Click')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CreateAnnualeSede')\n", " - ('Create<PERSON><PERSON><PERSON>', 'CreateRiprevisioneSede')\n", " - ('C<PERSON><PERSON><PERSON><PERSON>', 'CreatePluriennaleSede')\n", "\n", "Metodi totali nella vertical slice: 27\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CheckInsert')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CreateAnnualeImpianto')\n", " - ('CreateBudget', 'CreateAnnualeImpianto_ConBloccoNoPdmProBudget')\n", " - ('Create<PERSON><PERSON><PERSON>', 'CreateAnnualeImpianto_old')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CreateAnnualeSede')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CreatePluriennaleImpianto')\n", " - ('C<PERSON><PERSON><PERSON><PERSON>', 'CreatePluriennaleSede')\n", " - ('Create<PERSON><PERSON><PERSON>', 'CreateRiprevisioneImpianto')\n", " - ('Create<PERSON><PERSON><PERSON>', 'CreateRiprevisioneSede')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'DdlCentraliNucleo_SelectedIndexChanged')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'DdlTipologiaEsercizio_SelectedIndexChanged')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Fill<PERSON>dro')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Fill<PERSON><PERSON><PERSON>')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'FillUP')\n", " - ('Create<PERSON>udget', 'InitializeComponent')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'OnInit')\n", " - ('<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>_Load')\n", " - ('CreateBudget', 'btnCrea_Click')\n", " - ('CreateBudget', 'btnProcedi_Click')\n", " - ('DlgWinBox', 'Inform')\n", " - ('DlgWinBox', 'ShowError')\n", " - ('DlgWinBox', 'ShowInformationMessage')\n", " - ('DlgWinBox', 'ShowMessage')\n", " - ('MessaggiApplicativi', 'ContaParametri')\n", " - ('MessaggiApplicativi', 'Messaggio')\n", " - ('UP', 'ListaToString')\n", " - ('UP', 'ToString')\n", "\n", "File totali nella vertical slice: 4\n", " - /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/NBDO/UC/Pianificazione/Budget/CreateBudget.ascx.cs\n", " - /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/NBDOBusinessLogic/Integrazione/ClassiChiamata/ImportazioneNMO13.cs\n", " - /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/NBDOLib/Common/MessaggiApplicativi.cs\n", " - /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/NBDOLib/DLGWINBOX.CS\n", "Copiati 4 file in /Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/verticale\n"]}], "source": ["import json\n", "import os\n", "import shutil\n", "from collections import deque\n", "\n", "# ======================================================\n", "# CONFIGURAZIONE\n", "# ======================================================\n", "# Percorso al file JSON (32 MB) con la struttura di tutto il codice\n", "JSON_PATH = \"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/report.json\"\n", "\n", "# Scegli se vuoi basarti su una CLASSE specifica o un FILE specifico:\n", "#  - Se \"use_class\" è True, allora cercherà tutti i metodi di `CLASS_NAME`\n", "#  - Se \"use_class\" è False, cercherà tutti i metodi del `FILE_NAME`\n", "use_class = False\n", "\n", "CLASS_NAME = \"CreateBudget\"                # Se stai usando la classe \"CreateBudget\"\n", "FILE_NAME  = \"CreateBudget.ascx.cs\"        # Se volessi basarti sul file\n", "\n", "# Dove copiare i file risultanti del vertical slice (facoltativo)\n", "OUTPUT_FOLDER = \"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/verticale\"\n", "\n", "\n", "# ======================================================\n", "# FUNZIONI\n", "# ======================================================\n", "def carica_dati(json_path):\n", "    \"\"\"\n", "    Legge il file JSON e restituisce la lista/dizionario in memoria.\n", "    \"\"\"\n", "    with open(json_path, \"r\", encoding=\"utf-8\") as f:\n", "        data = json.load(f)\n", "    return data\n", "\n", "\n", "def indicizza_dati(data):\n", "    \"\"\"\n", "    Crea un indice: (<PERSON>Name, MethodName) -> { info }\n", "    Dove info contiene 'FilePath', 'ClassName', 'MethodName', 'Dependencies', ecc.\n", "    \"\"\"\n", "    index = {}\n", "    for entry in data:\n", "        file_path = entry[\"FilePath\"]\n", "        class_name = entry[\"ClassName\"]\n", "        methods = entry.get(\"Methods\", [])\n", "        for m in methods:\n", "            key = (class_name, m[\"MethodName\"])\n", "            index[key] = {\n", "                \"FilePath\": file_path,\n", "                \"ClassName\": class_name,\n", "                \"MethodName\": m[\"MethodName\"],\n", "                \"LineCount\": m.get(\"LineCount\", 0),\n", "                \"CyclomaticComplexity\": m.get(\"CyclomaticComplexity\", 0),\n", "                \"Dependencies\": m.get(\"Dependencies\", [])\n", "            }\n", "    return index\n", "\n", "\n", "def raccogli_metodi_di_classe(index, classe):\n", "    \"\"\"\n", "    Ritorna la lista di (className, methodName) per TUTTI i metodi\n", "    che appartengono a 'classe'.\n", "    \"\"\"\n", "    results = []\n", "    for (cls, mth), info in index.items():\n", "        if cls == classe:\n", "            results.append((cls, mth))\n", "    return results\n", "\n", "\n", "def raccogli_metodi_di_file(index, file_name):\n", "    \"\"\"\n", "    Ritorna la lista di (className, methodName) per TUTTI i metodi\n", "    che appartengono al file 'file_name' (match su info[\"FilePath\"]).\n", "    \"\"\"\n", "    results = []\n", "    for (cls, mth), info in index.items():\n", "        # Controlliamo se l'ultimo pezzo del path corrisponde a file_name\n", "        # oppure se preferisci un confronto su \"file_name\" in info[\"FilePath\"]\n", "        if os.path.basename(info[\"FilePath\"]) == file_name:\n", "            results.append((cls, mth))\n", "    return results\n", "\n", "\n", "def estrai_verticale_multiplo(index, start_keys):\n", "    \"\"\"\n", "    Esegue BFS partendo da un elenco di metodi 'start_keys' (ognuno è una (ClassName, MethodName)).\n", "    Raccoglie tutti i metodi e i file .cs toccati dalle dipendenze.\n", "    \"\"\"\n", "    visited_methods = set()\n", "    visited_files = set()\n", "    queue = deque(start_keys)\n", "\n", "    while queue:\n", "        current = queue.popleft()\n", "        if current in visited_methods:\n", "            continue\n", "\n", "        visited_methods.add(current)\n", "\n", "        info = index.get(current)\n", "        if not info:\n", "            # Non trovato nell'indice -> dipendenza di libreria\n", "            continue\n", "\n", "        visited_files.add(info[\"FilePath\"])\n", "\n", "        # Per ogni dipendenza, vediamo se è un metodo definito nel JSON\n", "        for dep in info[\"Dependencies\"]:\n", "            # Se c'è un \".\", ipotizziamo \"Classe.Metodo\"\n", "            if \".\" in dep:\n", "                parts = dep.split(\".\")\n", "                # potresti avere \"NomeClasse.Metodo\" o \"Namespace.NomeClasse.Metodo\"\n", "                # Per semplicità:\n", "                dep_class = parts[0]\n", "                dep_method = parts[-1]\n", "                dep_key = (dep_class, dep_method)\n", "                if dep_key in index:\n", "                    queue.append(dep_key)\n", "            else:\n", "                # Altrimenti consideriamo la stessa classe\n", "                same_class_key = (info[\"ClassName\"], dep)\n", "                if same_class_key in index:\n", "                    queue.append(same_class_key)\n", "\n", "    return visited_methods, visited_files\n", "\n", "\n", "def copia_file_verticale(files_list, output_folder):\n", "    flist = list(files_list)\n", "\n", "    # Imposta la cartella che consideri \"root\" del codice sorgente\n", "    SRC_ROOT = \"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017\"\n", "\n", "    for src in flist:\n", "        if not os.path.isfile(src):\n", "            print(f\"ATTENZIONE: file non trovato: {src}\")\n", "            continue\n", "\n", "        try:\n", "            rel_path = os.path.relpath(src, SRC_ROOT)\n", "        except ValueError:\n", "            rel_path = os.path.basename(src)\n", "\n", "        dest_path = os.path.join(output_folder, rel_path)\n", "\n", "        # Se è lo stesso file, salta\n", "        if os.path.exists(dest_path):\n", "            try:\n", "                if os.path.samefile(src, dest_path):\n", "                    print(f\"SKIP: {src} e {dest_path} sono lo stesso file.\")\n", "                    continue\n", "            except FileNotFoundError:\n", "                pass  # Se 'dest_path' non esiste ancora\n", "\n", "        os.makedirs(os.path.dirname(dest_path), exist_ok=True)\n", "        shutil.copy2(src, dest_path)\n", "\n", "    print(f\"Copiati {len(flist)} file in {output_folder}\")\n", "\n", "\n", "\n", "\n", "def main():\n", "    # 1) Carica J<PERSON>\n", "    data = carica_dati(JSON_PATH)\n", "\n", "    # 2) <PERSON><PERSON> indice\n", "    index = indicizza_dati(data)\n", "\n", "    # 3) <PERSON><PERSON><PERSON> tutti i metodi \"entry point\"\n", "    if use_class:\n", "        # ricostruiamo TUTTI i metodi di una certa classe\n", "        start_methods = raccogli_metodi_di_classe(index, CLASS_NAME)\n", "    else:\n", "        # ricostruiamo TUTTI i metodi di un certo file\n", "        start_methods = raccogli_metodi_di_file(index, FILE_NAME)\n", "\n", "    if not start_methods:\n", "        print(\"<PERSON><PERSON>un metodo trovato per i criteri specificati!\")\n", "        return\n", "\n", "    print(f\"<PERSON><PERSON><PERSON> di partenza (entry points): {len(start_methods)}\")\n", "    for sm in start_methods:\n", "        print(\" -\", sm)\n", "\n", "    # 4) BFS su TUTTI i metodi entry point\n", "    visited_methods, visited_files = estrai_verticale_multiplo(index, start_methods)\n", "\n", "    print(\"\\nMetodi totali nella vertical slice:\", len(visited_methods))\n", "    for vm in sorted(visited_methods):\n", "        print(\" -\", vm)\n", "\n", "    print(\"\\nFile totali nella vertical slice:\", len(visited_files))\n", "    for vf in sorted(visited_files):\n", "        print(\" -\", vf)\n", "\n", "    # 5) (Opzionale) Copia i file in OUTPUT_FOLDER\n", "    if OUTPUT_FOLDER:\n", "        copia_file_verticale(visited_files, OUTPUT_FOLDER)\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "bbba8eb4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}